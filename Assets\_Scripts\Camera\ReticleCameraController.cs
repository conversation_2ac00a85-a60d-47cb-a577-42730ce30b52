using UnityEngine;
using BTR.CombatUI;

namespace BTR.CameraControl
{
    /// <summary>
    /// Controls camera movement based on reticle position by moving the camera target.
    /// Works with existing Cinemachine setup to provide smooth camera following.
    /// </summary>
    public class ReticleCameraController : MonoBehaviour
    {
        [Header("Movement Sensitivity")]
        [SerializeField, Range(0f, 20f)] private float horizontalSensitivity = 5f;
        [SerializeField, Range(0f, 20f)] private float verticalSensitivity = 8f;
        
        [Header("Movement Bounds")]
        [SerializeField, Range(0f, 50f)] private float maxHorizontalOffset = 10f;
        [SerializeField, Range(0f, 50f)] private float maxVerticalOffset = 15f;
        
        [Header("Movement Settings")]
        [SerializeField, Range(0.1f, 10f)] private float dampingSpeed = 2f;
        [SerializeField] private bool enableHorizontalMovement = true;
        [SerializeField] private bool enableVerticalMovement = true;
        
        [Header("Dead Zone (Optional)")]
        [SerializeField] private bool useDeadZone = false;
        [SerializeField, Range(0f, 0.5f)] private float deadZoneRadius = 0.1f;
        
        [Header("Debug")]
        [SerializeField] private bool showDebugInfo = false;
        
        // Internal state
        private Vector3 basePosition;
        private Vector3 targetOffset;
        private Vector3 currentOffset;
        private UIReticleController reticleController;
        
        // Properties for external access
        public Vector3 CurrentOffset => currentOffset;
        public bool IsEnabled { get; set; } = true;
        
        private void Awake()
        {
            // Store the original local position as our base
            basePosition = transform.localPosition;
        }
        
        private void Start()
        {
            // Find the reticle controller
            reticleController = UIReticleController.Instance;

            if (reticleController == null)
            {
                Debug.LogError($"[{GetType().Name}] UIReticleController.Instance not found. Camera movement will be disabled.");
            }
            else
            {
                Debug.Log($"[{GetType().Name}] Successfully found UIReticleController.Instance. Initial position: {reticleController.NormalizedScreenPosition}");
            }

            Debug.Log($"[{GetType().Name}] Initialized on GameObject: {gameObject.name}, Parent: {transform.parent?.name}, Base Position: {basePosition}");
        }
        
        private void Update()
        {
            if (!IsEnabled || reticleController == null)
            {
                if (showDebugInfo)
                {
                    Debug.Log($"[ReticleCameraController] Update skipped - IsEnabled: {IsEnabled}, reticleController: {reticleController != null}");
                }
                return;
            }

            UpdateTargetOffset();
            ApplySmoothedOffset();

            if (showDebugInfo)
            {
                Debug.Log($"[ReticleCameraController] Reticle: {reticleController.NormalizedScreenPosition:F3}, Target: {targetOffset:F3}, Current: {currentOffset:F3}, Position: {transform.localPosition:F3}");
            }
        }
        
        /// <summary>
        /// Calculates the target offset based on current reticle position
        /// </summary>
        private void UpdateTargetOffset()
        {
            // Get normalized reticle position (0-1 range)
            Vector2 reticleNormalized = reticleController.NormalizedScreenPosition;
            
            // Convert to centered coordinates (-0.5 to 0.5)
            Vector2 centeredPosition = reticleNormalized - Vector2.one * 0.5f;
            
            // Apply dead zone if enabled
            if (useDeadZone)
            {
                float distance = centeredPosition.magnitude;
                if (distance < deadZoneRadius)
                {
                    centeredPosition = Vector2.zero;
                }
                else
                {
                    // Scale to remove dead zone effect
                    centeredPosition = centeredPosition.normalized * 
                        ((distance - deadZoneRadius) / (0.5f - deadZoneRadius));
                }
            }
            
            // Calculate target offset
            targetOffset = new Vector3(
                enableHorizontalMovement ? centeredPosition.x * horizontalSensitivity : 0f,
                enableVerticalMovement ? centeredPosition.y * verticalSensitivity : 0f,
                0f
            );
            
            // Apply bounds
            targetOffset.x = Mathf.Clamp(targetOffset.x, -maxHorizontalOffset, maxHorizontalOffset);
            targetOffset.y = Mathf.Clamp(targetOffset.y, -maxVerticalOffset, maxVerticalOffset);
        }
        
        /// <summary>
        /// Smoothly applies the calculated offset to the camera target position
        /// </summary>
        private void ApplySmoothedOffset()
        {
            // Smooth towards target offset
            currentOffset = Vector3.Lerp(currentOffset, targetOffset, dampingSpeed * Time.deltaTime);
            
            // Apply to transform
            transform.localPosition = basePosition + currentOffset;
        }
        
        /// <summary>
        /// Resets the camera target to its base position
        /// </summary>
        public void ResetToCenter()
        {
            targetOffset = Vector3.zero;
            currentOffset = Vector3.zero;
            transform.localPosition = basePosition;
        }
        
        /// <summary>
        /// Sets movement sensitivity for both axes
        /// </summary>
        public void SetSensitivity(float horizontal, float vertical)
        {
            horizontalSensitivity = Mathf.Max(0f, horizontal);
            verticalSensitivity = Mathf.Max(0f, vertical);
        }
        
        /// <summary>
        /// Sets movement bounds for both axes
        /// </summary>
        public void SetBounds(float maxHorizontal, float maxVertical)
        {
            maxHorizontalOffset = Mathf.Max(0f, maxHorizontal);
            maxVerticalOffset = Mathf.Max(0f, maxVertical);
        }
        
        /// <summary>
        /// Enables or disables movement on specific axes
        /// </summary>
        public void SetAxisEnabled(bool horizontal, bool vertical)
        {
            enableHorizontalMovement = horizontal;
            enableVerticalMovement = vertical;
        }
        
        // Debug visualization
        private void OnDrawGizmosSelected()
        {
            if (!Application.isPlaying) return;
            
            // Draw movement bounds
            Gizmos.color = Color.yellow;
            Vector3 worldPos = transform.position;
            
            // Draw horizontal bounds
            if (enableHorizontalMovement)
            {
                Gizmos.DrawLine(
                    worldPos + Vector3.left * maxHorizontalOffset,
                    worldPos + Vector3.right * maxHorizontalOffset
                );
            }
            
            // Draw vertical bounds
            if (enableVerticalMovement)
            {
                Gizmos.DrawLine(
                    worldPos + Vector3.down * maxVerticalOffset,
                    worldPos + Vector3.up * maxVerticalOffset
                );
            }
            
            // Draw current offset
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(worldPos, 0.5f);
            
            // Draw target offset
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(transform.parent.position + basePosition + targetOffset, 0.3f);
        }
        
        private void OnGUI()
        {
            if (!showDebugInfo || !Application.isPlaying) return;
            
            GUILayout.BeginArea(new Rect(10, 10, 300, 150));
            GUILayout.Label("=== Reticle Camera Controller ===");
            GUILayout.Label($"Enabled: {IsEnabled}");
            GUILayout.Label($"Reticle Pos: {(reticleController?.NormalizedScreenPosition ?? Vector2.zero):F2}");
            GUILayout.Label($"Target Offset: {targetOffset:F2}");
            GUILayout.Label($"Current Offset: {currentOffset:F2}");
            GUILayout.Label($"Final Position: {transform.localPosition:F2}");
            GUILayout.EndArea();
        }
    }
}
