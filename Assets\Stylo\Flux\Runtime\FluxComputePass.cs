using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityEngine.Rendering.RenderGraphModule;
using Unity.Collections;

namespace Stylo.Flux.Universal
{
    /// <summary>
    /// Compute shader-based render pass for Flux effect processing.
    /// Handles Encode and Decode operations using compute kernels for improved performance.
    /// </summary>
    public class FluxComputePass : ScriptableRenderPass
    {
        private const string PROFILER_TAG = "Flux Compute Pass";
        private ProfilingSampler m_ProfilingSampler = new ProfilingSampler(PROFILER_TAG);

        // Compute shader and kernel IDs
        private ComputeShader m_FluxComputeShader;
        private int m_EncodeKernelId;
        private int m_DecodeKernelId;
        private int m_EncodeKernelMobileId;
        private int m_DecodeKernelMobileId;
        private int m_DebugMotionKernelId;
        private int m_DebugCompressionKernelId;

        // Compute buffer management
        private ComputeBuffer m_IntermediateBuffer;
        private ComputeBuffer m_DCTCoefficientsBuffer;
        private ComputeBuffer m_UniformsBuffer;

        // Thread group calculations
        private Vector3Int m_ThreadGroups;
        private Vector3Int m_ThreadGroupsMobile;

        // Platform-specific settings
        private bool m_UseMobileOptimization;
        private bool m_UseSharedMemory;
        private int m_OptimalThreadGroupSize;

        // Adaptive quality system
        private AdaptiveQualitySettings m_QualitySettings;
        private PerformanceMetrics m_PerformanceMetrics;

        // Resource management
        private TextureHandle m_EncodeOutputTexture;
        private TextureHandle m_DecodeOutputTexture;
        private RTHandle m_DebugOutputTexture;

        // Error handling and fallback
        private bool m_ComputeShaderSupported;
        private bool m_InitializationFailed;
        private System.Action m_FallbackCallback;

        public struct AdaptiveQualitySettings
        {
            public int blockSize;
            public int threadGroupSize;
            public bool useSharedMemory;
            public float qualityLevel;
            public bool enableMobileOptimization;
        }

        public struct PerformanceMetrics
        {
            public float encodeTime;
            public float decodeTime;
            public float memoryBandwidth;
            public float occupancyRate;
            public int dispatchCount;
        }

        public FluxComputePass(ComputeShader computeShader, System.Action fallbackCallback = null)
        {
            m_FluxComputeShader = computeShader;
            m_FallbackCallback = fallbackCallback;

            profilingSampler = m_ProfilingSampler;
            renderPassEvent = RenderPassEvent.BeforeRenderingPostProcessing;

            InitializeComputeShader();
            DetectPlatformCapabilities();
            InitializeAdaptiveQuality();
        }

        private void InitializeComputeShader()
        {
            if (m_FluxComputeShader == null)
            {
                Debug.LogError("FluxComputePass: Compute shader is null");
                m_InitializationFailed = true;
                return;
            }

            try
            {
                // Find kernel IDs
                m_EncodeKernelId = m_FluxComputeShader.FindKernel("CSEncodeMain");
                m_DecodeKernelId = m_FluxComputeShader.FindKernel("CSDecodeMain");
                m_EncodeKernelMobileId = m_FluxComputeShader.FindKernel("CSEncodeMainMobile");
                m_DecodeKernelMobileId = m_FluxComputeShader.FindKernel("CSDecodeMainMobile");
                m_DebugMotionKernelId = m_FluxComputeShader.FindKernel("CSDebugMotionVectors");
                m_DebugCompressionKernelId = m_FluxComputeShader.FindKernel("CSDebugCompression");

                m_ComputeShaderSupported = true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"FluxComputePass: Failed to initialize compute shader kernels: {e.Message}");
                m_InitializationFailed = true;
                m_ComputeShaderSupported = false;
            }
        }

        private void DetectPlatformCapabilities()
        {
            // Check compute shader support
            m_ComputeShaderSupported = SystemInfo.supportsComputeShaders;

            // Platform-specific optimizations
            bool isMobile = Application.isMobilePlatform;
            bool isConsolePlatform = Application.platform == RuntimePlatform.PS5 || Application.platform == RuntimePlatform.XboxOne;

            if (isMobile)
            {
                m_UseMobileOptimization = true;
                m_UseSharedMemory = false; // Shared memory can be limited on mobile
                m_OptimalThreadGroupSize = 8; // Smaller thread groups for mobile
            }
            else if (isConsolePlatform)
            {
                m_UseMobileOptimization = false;
                m_UseSharedMemory = true;
                m_OptimalThreadGroupSize = 16;
            }
            else // Desktop
            {
                m_UseMobileOptimization = false;
                m_UseSharedMemory = true;
                m_OptimalThreadGroupSize = 16;
            }

            // GPU-specific optimizations
            string gpuName = SystemInfo.graphicsDeviceName.ToLower();
            if (gpuName.Contains("mali") || gpuName.Contains("adreno") || gpuName.Contains("powervr"))
            {
                // Mobile GPU optimizations
                m_UseSharedMemory = false;
                m_OptimalThreadGroupSize = 8;
            }
            else if (gpuName.Contains("intel"))
            {
                // Intel GPU optimizations
                m_OptimalThreadGroupSize = 8; // Intel GPUs often prefer smaller thread groups
            }
        }

        private void InitializeAdaptiveQuality()
        {
            m_QualitySettings = new AdaptiveQualitySettings
            {
                blockSize = m_UseMobileOptimization ? 4 : 8,
                threadGroupSize = m_OptimalThreadGroupSize,
                useSharedMemory = m_UseSharedMemory,
                qualityLevel = 1.0f,
                enableMobileOptimization = m_UseMobileOptimization
            };

            m_PerformanceMetrics = new PerformanceMetrics();
        }

        public void Setup(RTHandle sourceTexture, RTHandle destinationTexture, FluxComputeUniforms uniforms)
        {
            if (m_InitializationFailed || !m_ComputeShaderSupported)
            {
                m_FallbackCallback?.Invoke();
                return;
            }

            // Validate texture formats for UAV compatibility
            if (!ValidateTextureForUAV(sourceTexture) || !ValidateTextureForUAV(destinationTexture))
            {
                Debug.LogError("FluxComputePass: Texture format not compatible with UAV access");
                m_FallbackCallback?.Invoke();
                return;
            }

            // Calculate thread groups based on texture size
            int textureWidth = destinationTexture.rt.width;
            int textureHeight = destinationTexture.rt.height;

            int threadGroupSize = m_QualitySettings.threadGroupSize;
            m_ThreadGroups = new Vector3Int(
                Mathf.CeilToInt((float)textureWidth / threadGroupSize),
                Mathf.CeilToInt((float)textureHeight / threadGroupSize),
                1
            );

            if (m_UseMobileOptimization)
            {
                int mobileThreadGroupSize = m_QualitySettings.threadGroupSize / 2;
                m_ThreadGroupsMobile = new Vector3Int(
                    Mathf.CeilToInt((float)textureWidth / mobileThreadGroupSize),
                    Mathf.CeilToInt((float)textureHeight / mobileThreadGroupSize),
                    1
                );
            }

            // Initialize compute buffers if needed
            InitializeComputeBuffers(textureWidth, textureHeight);

            // Update uniforms
            UpdateComputeUniforms(uniforms);
        }

        private bool ValidateTextureForUAV(RTHandle texture)
        {
            if (texture?.rt == null) return false;

            // Check if format supports UAV access
            RenderTextureFormat format = texture.rt.format;
            return format == RenderTextureFormat.ARGB32 ||
                   format == RenderTextureFormat.ARGBFloat ||
                   format == RenderTextureFormat.ARGBHalf ||
                   format == RenderTextureFormat.RGB111110Float ||
                   format == RenderTextureFormat.RGFloat ||
                   format == RenderTextureFormat.RGHalf;
        }

        private void InitializeComputeBuffers(int width, int height)
        {
            int pixelCount = width * height;

            // Initialize intermediate buffer for DCT processing
            if (m_IntermediateBuffer == null || m_IntermediateBuffer.count < pixelCount)
            {
                m_IntermediateBuffer?.Release();
                m_IntermediateBuffer = new ComputeBuffer(pixelCount, sizeof(float) * 4, ComputeBufferType.Structured);
            }

            // Initialize DCT coefficients buffer
            int dctBufferSize = Mathf.CeilToInt((float)pixelCount / 64) * 64; // 8x8 blocks
            if (m_DCTCoefficientsBuffer == null || m_DCTCoefficientsBuffer.count < dctBufferSize)
            {
                m_DCTCoefficientsBuffer?.Release();
                m_DCTCoefficientsBuffer = new ComputeBuffer(dctBufferSize, sizeof(float), ComputeBufferType.Structured);
            }

            // Initialize uniforms buffer
            if (m_UniformsBuffer == null)
            {
                m_UniformsBuffer = new ComputeBuffer(1, System.Runtime.InteropServices.Marshal.SizeOf<FluxComputeUniforms>(), ComputeBufferType.Constant);
            }
        }

        private void UpdateComputeUniforms(FluxComputeUniforms uniforms)
        {
            // Apply adaptive quality adjustments
            uniforms.BlockSize = m_QualitySettings.blockSize;
            uniforms.ThreadGroupCountX = m_ThreadGroups.x;
            uniforms.ThreadGroupCountY = m_ThreadGroups.y;
            uniforms.ComputeQuality = m_QualitySettings.qualityLevel;
            uniforms.UseSharedMemory = m_QualitySettings.useSharedMemory ? 1 : 0;
            uniforms.MobileOptimization = m_QualitySettings.enableMobileOptimization ? 1.0f : 0.0f;

            // Update buffer
            if (m_UniformsBuffer != null)
            {
                m_UniformsBuffer.SetData(new FluxComputeUniforms[] { uniforms });
            }
        }

        public void Setup(TextureHandle inputTexture, TextureHandle encodedTexture, TextureHandle motionVectorTexture,
                         TextureHandle previousFrameTexture, TextureHandle encodeOutput, TextureHandle decodeOutput)
        {
            // Store texture references for compute pass
            m_InputTexture = inputTexture;
            m_EncodedTexture = encodedTexture;
            m_MotionVectorTexture = motionVectorTexture;
            m_PreviousFrameTexture = previousFrameTexture;
            m_EncodeOutputTexture = encodeOutput;
            m_DecodeOutputTexture = decodeOutput;
        }

        // Add texture handle fields
        private TextureHandle m_InputTexture;
        private TextureHandle m_EncodedTexture;
        private TextureHandle m_MotionVectorTexture;
        private TextureHandle m_PreviousFrameTexture;

        public override void RecordRenderGraph(RenderGraph renderGraph, ContextContainer frameData)
        {
            if (m_InitializationFailed || !m_ComputeShaderSupported)
            {
                m_FallbackCallback?.Invoke();
                return;
            }

            using (var builder = renderGraph.AddComputePass<FluxComputePassData>(PROFILER_TAG, out var passData))
            {
                // Setup pass data
                passData.computeShader = m_FluxComputeShader;
                passData.encodeKernelId = m_UseMobileOptimization ? m_EncodeKernelMobileId : m_EncodeKernelId;
                passData.decodeKernelId = m_UseMobileOptimization ? m_DecodeKernelMobileId : m_DecodeKernelId;
                passData.threadGroups = m_UseMobileOptimization ? m_ThreadGroupsMobile : m_ThreadGroups;
                passData.uniformsBuffer = m_UniformsBuffer;
                passData.intermediateBuffer = m_IntermediateBuffer;
                passData.dctCoefficientsBuffer = m_DCTCoefficientsBuffer;

                // Set texture references
                passData.inputTexture = m_InputTexture;
                passData.encodedTexture = m_EncodedTexture;
                passData.motionVectorTexture = m_MotionVectorTexture;
                passData.previousFrameTexture = m_PreviousFrameTexture;
                passData.encodeOutput = m_EncodeOutputTexture;
                passData.decodeOutput = m_DecodeOutputTexture;

                // Set render graph dependencies
                builder.AllowPassCulling(false);
                builder.AllowGlobalStateModification(true);

                // Set execution callback
                builder.SetRenderFunc((FluxComputePassData data, ComputeGraphContext context) =>
                {
                    ExecuteComputePass(data, context);
                });
            }
        }

        private void ExecuteComputePass(FluxComputePassData data, ComputeGraphContext context)
        {
            // Use the correct command buffer type for compute passes
            var cmd = context.cmd; // If context.cmd is already the correct type, no cast needed

            using (new ProfilingScope(cmd, m_ProfilingSampler))
            {
                // Begin performance measurement
                float startTime = Time.realtimeSinceStartup;

                try
                {
                    // Set compute buffers
                    cmd.SetComputeBufferParam(data.computeShader, data.encodeKernelId, "_IntermediateBuffer", data.intermediateBuffer);
                    cmd.SetComputeBufferParam(data.computeShader, data.encodeKernelId, "_DCTCoefficients", data.dctCoefficientsBuffer);
                    cmd.SetComputeConstantBufferParam(data.computeShader, "FluxComputeUniforms", data.uniformsBuffer, 0, data.uniformsBuffer.stride);

                    // Set texture parameters for encode kernel
                    if (data.inputTexture.IsValid())
                        cmd.SetComputeTextureParam(data.computeShader, data.encodeKernelId, "_InputTex", data.inputTexture);
                    if (data.encodeOutput.IsValid())
                        cmd.SetComputeTextureParam(data.computeShader, data.encodeKernelId, "_EncodeOutput", data.encodeOutput);

                    // Dispatch encode kernel
                    cmd.DispatchCompute(data.computeShader, data.encodeKernelId, data.threadGroups.x, data.threadGroups.y, data.threadGroups.z);

                    // Memory barrier
                    cmd.SetComputeBufferParam(data.computeShader, data.decodeKernelId, "_IntermediateBuffer", data.intermediateBuffer);
                    cmd.SetComputeBufferParam(data.computeShader, data.decodeKernelId, "_DCTCoefficients", data.dctCoefficientsBuffer);

                    // Set texture parameters for decode kernel
                    if (data.encodedTexture.IsValid())
                        cmd.SetComputeTextureParam(data.computeShader, data.decodeKernelId, "_EncodedTex", data.encodedTexture);
                    if (data.motionVectorTexture.IsValid())
                        cmd.SetComputeTextureParam(data.computeShader, data.decodeKernelId, "_MotionVectorTex", data.motionVectorTexture);
                    if (data.previousFrameTexture.IsValid())
                        cmd.SetComputeTextureParam(data.computeShader, data.decodeKernelId, "_PrevTex", data.previousFrameTexture);
                    if (data.decodeOutput.IsValid())
                        cmd.SetComputeTextureParam(data.computeShader, data.decodeKernelId, "_DecodeOutput", data.decodeOutput);

                    // Dispatch decode kernel
                    cmd.DispatchCompute(data.computeShader, data.decodeKernelId, data.threadGroups.x, data.threadGroups.y, data.threadGroups.z);

                    // Update performance metrics
                    float endTime = Time.realtimeSinceStartup;
                    m_PerformanceMetrics.encodeTime = (endTime - startTime) * 0.5f;
                    m_PerformanceMetrics.decodeTime = (endTime - startTime) * 0.5f;
                    m_PerformanceMetrics.dispatchCount = 2;

                    // Adaptive quality adjustment based on performance
                    AdjustQualityBasedOnPerformance();
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"FluxComputePass: Error during compute execution: {e.Message}");
                    m_FallbackCallback?.Invoke();
                }
            }
        }

        private void AdjustQualityBasedOnPerformance()
        {
            float totalTime = m_PerformanceMetrics.encodeTime + m_PerformanceMetrics.decodeTime;
            float targetTime = 1.0f / 60.0f * 0.5f; // Target half frame time for this pass

            if (totalTime > targetTime * 1.2f) // Performance too slow
            {
                if (m_QualitySettings.blockSize > 2)
                {
                    m_QualitySettings.blockSize = Mathf.Max(2, m_QualitySettings.blockSize - 1);
                }
                if (m_QualitySettings.qualityLevel > 0.5f)
                {
                    m_QualitySettings.qualityLevel = Mathf.Max(0.5f, m_QualitySettings.qualityLevel - 0.1f);
                }
            }
            else if (totalTime < targetTime * 0.8f) // Performance headroom available
            {
                if (m_QualitySettings.blockSize < 8)
                {
                    m_QualitySettings.blockSize = Mathf.Min(8, m_QualitySettings.blockSize + 1);
                }
                if (m_QualitySettings.qualityLevel < 1.0f)
                {
                    m_QualitySettings.qualityLevel = Mathf.Min(1.0f, m_QualitySettings.qualityLevel + 0.05f);
                }
            }
        }

        public void ExecuteDebugPass(CommandBuffer cmd, RTHandle debugOutput, int debugMode)
        {
            if (m_InitializationFailed || !m_ComputeShaderSupported) return;

            int kernelId = debugMode == 0 ? m_DebugMotionKernelId : m_DebugCompressionKernelId;

            cmd.SetComputeTextureParam(m_FluxComputeShader, kernelId, "_DebugOutput", debugOutput);
            cmd.DispatchCompute(m_FluxComputeShader, kernelId, m_ThreadGroups.x, m_ThreadGroups.y, m_ThreadGroups.z);
        }

        public PerformanceMetrics GetPerformanceMetrics()
        {
            return m_PerformanceMetrics;
        }

        public AdaptiveQualitySettings GetQualitySettings()
        {
            return m_QualitySettings;
        }

        public void SetQualitySettings(AdaptiveQualitySettings settings)
        {
            m_QualitySettings = settings;
        }

        public override void OnCameraCleanup(CommandBuffer cmd)
        {
            // Clean up compute resources
        }

        public void Dispose()
        {
            m_IntermediateBuffer?.Release();
            m_DCTCoefficientsBuffer?.Release();
            m_UniformsBuffer?.Release();

            // TextureHandle is a value type and doesn't need null checking
            // m_EncodeOutputTexture and m_DecodeOutputTexture are managed by RenderGraph
            m_DebugOutputTexture?.Release();
        }

        private class FluxComputePassData
        {
            public ComputeShader computeShader;
            public int encodeKernelId;
            public int decodeKernelId;
            public Vector3Int threadGroups;
            public ComputeBuffer uniformsBuffer;
            public ComputeBuffer intermediateBuffer;
            public ComputeBuffer dctCoefficientsBuffer;

            // Texture parameters for compute kernels - use TextureHandle for render graph
            public TextureHandle inputTexture;
            public TextureHandle encodedTexture;
            public TextureHandle motionVectorTexture;
            public TextureHandle previousFrameTexture;
            public TextureHandle encodeOutput;
            public TextureHandle decodeOutput;
        }
    }
}
