using UnityEngine;
using Unity.Cinemachine;
using BTR.CombatUI;

namespace BTR.CameraControl
{
    /// <summary>
    /// Alternative approach: Creates a dynamic offset target for reticle-based camera movement.
    /// Works by creating an invisible target that moves based on reticle position,
    /// then adds it to the Cinemachine Target Group.
    /// </summary>
    public class ReticleCameraControllerV2 : MonoBehaviour
    {
        [Header("Movement Sensitivity")]
        [SerializeField, Range(0f, 20f)] private float horizontalSensitivity = 5f;
        [SerializeField, Range(0f, 20f)] private float verticalSensitivity = 8f;
        
        [Header("Movement Bounds")]
        [SerializeField, Range(0f, 50f)] private float maxHorizontalOffset = 10f;
        [SerializeField, Range(0f, 50f)] private float maxVerticalOffset = 15f;
        
        [Header("Movement Settings")]
        [SerializeField, Range(0.1f, 10f)] private float dampingSpeed = 2f;
        [SerializeField] private bool enableHorizontalMovement = true;
        [SerializeField] private bool enableVerticalMovement = true;
        
        [Header("Target Group Settings")]
        [SerializeField] private CinemachineTargetGroup targetGroup;
        [SerializeField, Range(0f, 2f)] private float offsetTargetWeight = 0.3f;
        [SerializeField, Range(0f, 10f)] private float offsetTargetRadius = 2f;
        
        [Header("Debug")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private bool createVisualIndicator = false;
        
        // Internal state
        private GameObject offsetTarget;
        private Transform playerTransform;
        private Vector3 targetOffset;
        private Vector3 currentOffset;
        private UIReticleController reticleController;
        private int targetGroupIndex = -1;
        
        // Properties for external access
        public Vector3 CurrentOffset => currentOffset;
        public bool IsEnabled { get; set; } = true;
        
        private void Awake()
        {
            // Find player transform (parent of this Camera Target)
            playerTransform = transform.parent;
            if (playerTransform == null)
            {
                Debug.LogError($"[{GetType().Name}] Camera Target must be a child of the player GameObject!");
                enabled = false;
                return;
            }
        }
        
        private void Start()
        {
            // Find components
            reticleController = UIReticleController.Instance;
            if (reticleController == null)
            {
                Debug.LogError($"[{GetType().Name}] UIReticleController.Instance not found!");
                enabled = false;
                return;
            }
            
            // Find target group if not assigned
            if (targetGroup == null)
            {
                targetGroup = FindFirstObjectByType<CinemachineTargetGroup>();
                if (targetGroup == null)
                {
                    Debug.LogError($"[{GetType().Name}] CinemachineTargetGroup not found!");
                    enabled = false;
                    return;
                }
            }
            
            CreateOffsetTarget();
            AddToTargetGroup();
            
            Debug.Log($"[{GetType().Name}] Initialized successfully. Player: {playerTransform.name}, TargetGroup: {targetGroup.name}");
        }
        
        private void CreateOffsetTarget()
        {
            // Create invisible GameObject for offset target
            offsetTarget = new GameObject("Reticle Camera Offset Target");
            offsetTarget.transform.SetParent(playerTransform);
            offsetTarget.transform.localPosition = Vector3.zero;
            
            // Add visual indicator if requested
            if (createVisualIndicator)
            {
                var sphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                sphere.transform.SetParent(offsetTarget.transform);
                sphere.transform.localPosition = Vector3.zero;
                sphere.transform.localScale = Vector3.one * 0.5f;
                
                // Make it semi-transparent
                var renderer = sphere.GetComponent<Renderer>();
                var material = new Material(Shader.Find("Standard"));
                material.color = new Color(1f, 0f, 0f, 0.5f);
                material.SetFloat("_Mode", 3); // Transparent mode
                material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
                material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
                material.SetInt("_ZWrite", 0);
                material.DisableKeyword("_ALPHATEST_ON");
                material.EnableKeyword("_ALPHABLEND_ON");
                material.DisableKeyword("_ALPHAPREMULTIPLY_ON");
                material.renderQueue = 3000;
                renderer.material = material;
                
                // Remove collider
                DestroyImmediate(sphere.GetComponent<Collider>());
            }
        }
        
        private void AddToTargetGroup()
        {
            // Add offset target to the target group
            var targets = new System.Collections.Generic.List<CinemachineTargetGroup.Target>();
            
            // Copy existing targets
            for (int i = 0; i < targetGroup.Targets.Length; i++)
            {
                targets.Add(targetGroup.Targets[i]);
            }
            
            // Add our offset target
            var newTarget = new CinemachineTargetGroup.Target
            {
                Object = offsetTarget.transform,
                Weight = offsetTargetWeight,
                Radius = offsetTargetRadius
            };
            targets.Add(newTarget);
            targetGroupIndex = targets.Count - 1;
            
            // Update target group
            targetGroup.Targets = targets.ToArray();
            
            Debug.Log($"[{GetType().Name}] Added offset target to group at index {targetGroupIndex}");
        }
        
        private void Update()
        {
            if (!IsEnabled || reticleController == null || offsetTarget == null) return;
            
            UpdateTargetOffset();
            ApplySmoothedOffset();
            
            if (showDebugInfo)
            {
                Debug.Log($"[ReticleCameraControllerV2] Reticle: {reticleController.NormalizedScreenPosition:F3}, Target: {targetOffset:F3}, Current: {currentOffset:F3}");
            }
        }
        
        private void UpdateTargetOffset()
        {
            // Get normalized reticle position (0-1 range)
            Vector2 reticleNormalized = reticleController.NormalizedScreenPosition;
            
            // Convert to centered coordinates (-0.5 to 0.5)
            Vector2 centeredPosition = reticleNormalized - Vector2.one * 0.5f;
            
            // Calculate target offset
            targetOffset = new Vector3(
                enableHorizontalMovement ? centeredPosition.x * horizontalSensitivity : 0f,
                enableVerticalMovement ? centeredPosition.y * verticalSensitivity : 0f,
                0f
            );
            
            // Apply bounds
            targetOffset.x = Mathf.Clamp(targetOffset.x, -maxHorizontalOffset, maxHorizontalOffset);
            targetOffset.y = Mathf.Clamp(targetOffset.y, -maxVerticalOffset, maxVerticalOffset);
        }
        
        private void ApplySmoothedOffset()
        {
            // Smooth towards target offset
            currentOffset = Vector3.Lerp(currentOffset, targetOffset, dampingSpeed * Time.deltaTime);
            
            // Apply to offset target position
            offsetTarget.transform.localPosition = currentOffset;
        }
        
        /// <summary>
        /// Resets the offset target to center position
        /// </summary>
        public void ResetToCenter()
        {
            targetOffset = Vector3.zero;
            currentOffset = Vector3.zero;
            if (offsetTarget != null)
            {
                offsetTarget.transform.localPosition = Vector3.zero;
            }
        }
        
        /// <summary>
        /// Updates the weight of the offset target in the target group
        /// </summary>
        public void SetOffsetWeight(float weight)
        {
            offsetTargetWeight = Mathf.Clamp01(weight);
            if (targetGroup != null && targetGroupIndex >= 0 && targetGroupIndex < targetGroup.Targets.Length)
            {
                var targets = targetGroup.Targets;
                targets[targetGroupIndex].Weight = offsetTargetWeight;
                targetGroup.Targets = targets;
            }
        }
        
        private void OnDestroy()
        {
            // Clean up offset target
            if (offsetTarget != null)
            {
                DestroyImmediate(offsetTarget);
            }
        }
        
        private void OnDrawGizmosSelected()
        {
            if (!Application.isPlaying || offsetTarget == null) return;
            
            // Draw movement bounds relative to player
            Gizmos.color = Color.yellow;
            Vector3 playerPos = playerTransform.position;
            
            // Draw horizontal bounds
            if (enableHorizontalMovement)
            {
                Gizmos.DrawLine(
                    playerPos + Vector3.left * maxHorizontalOffset,
                    playerPos + Vector3.right * maxHorizontalOffset
                );
            }
            
            // Draw vertical bounds
            if (enableVerticalMovement)
            {
                Gizmos.DrawLine(
                    playerPos + Vector3.down * maxVerticalOffset,
                    playerPos + Vector3.up * maxVerticalOffset
                );
            }
            
            // Draw current offset target position
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(offsetTarget.transform.position, 0.5f);
        }
    }
}
