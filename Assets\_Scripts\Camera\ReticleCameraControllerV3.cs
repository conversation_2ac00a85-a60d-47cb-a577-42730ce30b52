using UnityEngine;
using Unity.Cinemachine;
using BTR.CombatUI;

namespace BTR.CameraControl
{
    /// <summary>
    /// Direct approach: Modifies CinemachineFollow FollowOffset based on reticle position.
    /// This approach directly manipulates the camera's follow offset for immediate response.
    /// </summary>
    public class ReticleCameraControllerV3 : MonoBehaviour
    {
        [Header("Movement Sensitivity")]
        [SerializeField, Range(0f, 20f)] private float horizontalSensitivity = 5f;
        [SerializeField, Range(0f, 20f)] private float verticalSensitivity = 8f;
        
        [Header("Movement Bounds")]
        [SerializeField, Range(0f, 50f)] private float maxHorizontalOffset = 10f;
        [SerializeField, Range(0f, 50f)] private float maxVerticalOffset = 15f;
        
        [Header("Movement Settings")]
        [SerializeField, Range(0.1f, 10f)] private float dampingSpeed = 2f;
        [SerializeField] private bool enableHorizontalMovement = true;
        [SerializeField] private bool enableVerticalMovement = true;
        
        [Header("Cinemachine References")]
        [SerializeField] private CinemachineCamera virtualCamera;
        
        [Header("Debug")]
        [SerializeField] private bool showDebugInfo = false;
        
        // Internal state
        private CinemachineFollow followComponent;
        private Vector3 baseFollowOffset;
        private Vector3 targetOffset;
        private Vector3 currentOffset;
        private UIReticleController reticleController;
        
        // Properties for external access
        public Vector3 CurrentOffset => currentOffset;
        public bool IsEnabled { get; set; } = true;
        
        private void Start()
        {
            // Find components
            reticleController = UIReticleController.Instance;
            if (reticleController == null)
            {
                Debug.LogError($"[{GetType().Name}] UIReticleController.Instance not found!");
                enabled = false;
                return;
            }
            
            // Find virtual camera if not assigned
            if (virtualCamera == null)
            {
                virtualCamera = FindFirstObjectByType<CinemachineCamera>();
                if (virtualCamera == null)
                {
                    Debug.LogError($"[{GetType().Name}] CinemachineCamera not found!");
                    enabled = false;
                    return;
                }
            }
            
            // Get the CinemachineFollow component
            followComponent = virtualCamera.GetComponent<CinemachineFollow>();
            if (followComponent == null)
            {
                Debug.LogError($"[{GetType().Name}] CinemachineFollow component not found on virtual camera!");
                enabled = false;
                return;
            }
            
            // Store the base follow offset
            baseFollowOffset = followComponent.FollowOffset;
            
            Debug.Log($"[{GetType().Name}] Initialized successfully. Virtual Camera: {virtualCamera.name}, Base Offset: {baseFollowOffset}");
        }
        
        private void Update()
        {
            if (!IsEnabled || reticleController == null || followComponent == null) return;
            
            UpdateTargetOffset();
            ApplySmoothedOffset();
            
            if (showDebugInfo)
            {
                Debug.Log($"[ReticleCameraControllerV3] Reticle: {reticleController.NormalizedScreenPosition:F3}, Target: {targetOffset:F3}, Current: {currentOffset:F3}, Final Offset: {followComponent.FollowOffset:F3}");
            }
        }
        
        private void UpdateTargetOffset()
        {
            // Get normalized reticle position (0-1 range)
            Vector2 reticleNormalized = reticleController.NormalizedScreenPosition;
            
            // Convert to centered coordinates (-0.5 to 0.5)
            Vector2 centeredPosition = reticleNormalized - Vector2.one * 0.5f;
            
            // Calculate target offset
            targetOffset = new Vector3(
                enableHorizontalMovement ? centeredPosition.x * horizontalSensitivity : 0f,
                enableVerticalMovement ? centeredPosition.y * verticalSensitivity : 0f,
                0f
            );
            
            // Apply bounds
            targetOffset.x = Mathf.Clamp(targetOffset.x, -maxHorizontalOffset, maxHorizontalOffset);
            targetOffset.y = Mathf.Clamp(targetOffset.y, -maxVerticalOffset, maxVerticalOffset);
        }
        
        private void ApplySmoothedOffset()
        {
            // Smooth towards target offset
            currentOffset = Vector3.Lerp(currentOffset, targetOffset, dampingSpeed * Time.deltaTime);
            
            // Apply to CinemachineFollow offset
            followComponent.FollowOffset = baseFollowOffset + currentOffset;
        }
        
        /// <summary>
        /// Resets the camera offset to the original follow offset
        /// </summary>
        public void ResetToCenter()
        {
            targetOffset = Vector3.zero;
            currentOffset = Vector3.zero;
            if (followComponent != null)
            {
                followComponent.FollowOffset = baseFollowOffset;
            }
        }
        
        /// <summary>
        /// Sets movement sensitivity for both axes
        /// </summary>
        public void SetSensitivity(float horizontal, float vertical)
        {
            horizontalSensitivity = Mathf.Max(0f, horizontal);
            verticalSensitivity = Mathf.Max(0f, vertical);
        }
        
        /// <summary>
        /// Sets movement bounds for both axes
        /// </summary>
        public void SetBounds(float maxHorizontal, float maxVertical)
        {
            maxHorizontalOffset = Mathf.Max(0f, maxHorizontal);
            maxVerticalOffset = Mathf.Max(0f, maxVertical);
        }
        
        /// <summary>
        /// Enables or disables movement on specific axes
        /// </summary>
        public void SetAxisEnabled(bool horizontal, bool vertical)
        {
            enableHorizontalMovement = horizontal;
            enableVerticalMovement = vertical;
        }
        
        private void OnDrawGizmosSelected()
        {
            if (!Application.isPlaying || followComponent == null) return;
            
            // Draw movement bounds relative to follow target
            if (virtualCamera.TrackingTarget != null)
            {
                Gizmos.color = Color.yellow;
                Vector3 targetPos = virtualCamera.TrackingTarget.position;
                
                // Draw horizontal bounds
                if (enableHorizontalMovement)
                {
                    Gizmos.DrawLine(
                        targetPos + Vector3.left * maxHorizontalOffset,
                        targetPos + Vector3.right * maxHorizontalOffset
                    );
                }
                
                // Draw vertical bounds
                if (enableVerticalMovement)
                {
                    Gizmos.DrawLine(
                        targetPos + Vector3.down * maxVerticalOffset,
                        targetPos + Vector3.up * maxVerticalOffset
                    );
                }
                
                // Draw current camera position
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(virtualCamera.transform.position, 0.5f);
                
                // Draw target position with offset
                Gizmos.color = Color.green;
                Vector3 offsetPosition = targetPos + baseFollowOffset + currentOffset;
                Gizmos.DrawWireSphere(offsetPosition, 0.3f);
                Gizmos.DrawLine(targetPos, offsetPosition);
            }
        }
        
        private void OnGUI()
        {
            if (!showDebugInfo || !Application.isPlaying) return;
            
            GUILayout.BeginArea(new Rect(10, 10, 400, 200));
            GUILayout.Label("=== Reticle Camera Controller V3 ===");
            GUILayout.Label($"Enabled: {IsEnabled}");
            GUILayout.Label($"Reticle Pos: {(reticleController?.NormalizedScreenPosition ?? Vector2.zero):F2}");
            GUILayout.Label($"Target Offset: {targetOffset:F2}");
            GUILayout.Label($"Current Offset: {currentOffset:F2}");
            GUILayout.Label($"Base Follow Offset: {baseFollowOffset:F2}");
            GUILayout.Label($"Final Follow Offset: {(followComponent?.FollowOffset ?? Vector3.zero):F2}");
            GUILayout.EndArea();
        }
    }
}
